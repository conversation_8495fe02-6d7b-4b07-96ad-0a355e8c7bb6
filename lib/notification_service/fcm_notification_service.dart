import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:busaty_parents/notification_service/errors/error_handler.dart';
import 'package:busaty_parents/notification_service/errors/notification_exception.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';

import 'notification_payload.dart';
import 'notification_service_interface.dart';

class FCMNotificationService implements INotificationService {
  final FirebaseMessaging _firebaseMessaging;
  final _notificationController =
      StreamController<NotificationPayload>.broadcast();
  final _notificationTapController =
      StreamController<NotificationPayload>.broadcast();

  FCMNotificationService(this._firebaseMessaging);

  @override
  Future<void> initialize() async {
    try {
      // Request permission
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        throw NotificationException(
          'Notification permissions not granted',
          code: 'permissions_not_granted',
        );
      }

      // Get FCM token
      String? token = await _firebaseMessaging.getToken();
      if (token == null) {
        throw NotificationException(
          'Failed to get FCM token',
          code: 'fcm_token_null',
        );
      }
      Logger.firebase('FCM Token: $token');

      // Background messages are handled by LocalNotificationService
      // to avoid duplicate registrations and ensure consistent notification handling

      // We're not handling foreground messages here to avoid duplicate notifications
      // LocalNotificationService will handle both foreground and background messages

      // Handle notification taps
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  // Background and foreground messages are now handled by LocalNotificationService

  void _handleNotificationTap(RemoteMessage message) {
    try {
      final notification = NotificationPayload(
        title: message.notification?.title ?? '',
        body: message.notification?.body ?? '',
        data: message.data,
        imageUrl: message.notification?.android?.imageUrl,
      );
      _notificationTapController.add(notification);
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
    }
  }

  @override
  Future<void> showNotification(NotificationPayload payload) async {
    throw NotificationException(
      'FCM notifications must be sent from server',
      code: 'client_send_not_supported',
    );
  }

  @override
  Future<void> showScheduledNotification({
    required NotificationPayload payload,
    required DateTime scheduledDate,
  }) async {
    throw NotificationException(
      'FCM notifications must be scheduled from server',
      code: 'client_schedule_not_supported',
    );
  }

  @override
  Future<void> cancelNotification(int id) async {
    throw NotificationException(
      'Cannot cancel specific FCM notifications',
      code: 'cancel_not_supported',
    );
  }

  @override
  Future<void> cancelAllNotifications() async {
    try {
      await _firebaseMessaging.deleteToken();
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Stream<NotificationPayload> get onNotificationReceived =>
      _notificationController.stream;

  @override
  Stream<NotificationPayload> get onNotificationTapped =>
      _notificationTapController.stream;

  void dispose() {
    _notificationController.close();
    _notificationTapController.close();
  }
}
