import 'package:busaty_parents/notification_service/utils/logger.dart';

/// Utility class for managing notification deduplication
class NotificationDeduplication {
  static final Map<String, DateTime> _notificationHistory = <String, DateTime>{};
  static const int _maxHistorySize = 100;
  static const Duration _duplicateWindow = Duration(minutes: 5);

  /// Check if a notification should be shown based on its message ID
  /// Returns true if the notification should be shown, false if it's a duplicate
  static bool shouldShowNotification(String messageId) {
    final now = DateTime.now();

    // Clean up old entries periodically
    _cleanupOldEntries(now);

    // Check if this notification was recently shown
    if (_notificationHistory.containsKey(messageId)) {
      final lastShown = _notificationHistory[messageId]!;
      final timeDifference = now.difference(lastShown);

      if (timeDifference < _duplicateWindow) {
        Logger.w('Duplicate notification detected: $messageId (last shown ${timeDifference.inSeconds}s ago)');
        return false;
      }
    }

    // Record this notification
    _notificationHistory[messageId] = now;

    // Limit history size to prevent memory issues
    if (_notificationHistory.length > _maxHistorySize) {
      _removeOldestEntries();
    }

    Logger.i('Notification approved for display: $messageId');
    return true;
  }

  /// Clean up entries older than the duplicate window
  static void _cleanupOldEntries(DateTime now) {
    final cutoffTime = now.subtract(_duplicateWindow);
    _notificationHistory.removeWhere((key, timestamp) => timestamp.isBefore(cutoffTime));
  }

  /// Remove oldest entries to maintain history size limit
  static void _removeOldestEntries() {
    final sortedEntries = _notificationHistory.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));

    final entriesToRemove = _notificationHistory.length - _maxHistorySize;
    for (int i = 0; i < entriesToRemove; i++) {
      _notificationHistory.remove(sortedEntries[i].key);
    }
  }

  /// Clear all notification history (useful for testing or reset)
  static void clearHistory() {
    _notificationHistory.clear();
    Logger.i('Notification history cleared');
  }

  /// Get current history size (useful for debugging)
  static int get historySize => _notificationHistory.length;
}
