# Notification Service - Duplicate Prevention

## المشكلة
كانت الإشعارات تظهر مكررة عندما يكون التطبيق في الخلفية أو مقفل، مما يسبب إزعاج للمستخدمين.

## الحل المطبق

### 1. آلية منع التكرار (Deduplication)
- تم إنشاء فئة `NotificationDeduplication` لإدارة منع تكرار الإشعارات
- تتبع الإشعارات المعروضة مؤخراً باستخدام message ID
- نافذة زمنية 5 دقائق لمنع التكرار
- تنظيف تلقائي للسجلات القديمة

### 2. توحيد معالجة الإشعارات
- إزالة التسجيل المكرر لمعالجات الإشعارات
- معالجة موحدة للإشعارات في المقدمة والخلفية
- استخدام message ID كمعرف فريد للإشعارات

### 3. تحسينات التكوين
- تحديث AndroidManifest.xml لتحسين معالجة الإشعارات
- إضافة `android:directBootAware="true"` لخدمة Firebase
- تحسين إعدادات قنوات الإشعارات

## الملفات المحدثة

### الملفات الأساسية
- `lib/notification_service/local_notification_service.dart` - الخدمة الرئيسية للإشعارات
- `lib/notification_service/fcm_notification_service.dart` - خدمة FCM
- `lib/main.dart` - تهيئة التطبيق

### الملفات الجديدة
- `lib/notification_service/utils/notification_deduplication.dart` - آلية منع التكرار
- `test/notification_deduplication_test.dart` - اختبارات آلية منع التكرار

### ملفات التكوين
- `android/app/src/main/AndroidManifest.xml` - تحسينات Android

## كيفية العمل

### 1. عند استلام إشعار جديد:
```dart
// فحص ما إذا كان الإشعار مكرر
if (!NotificationDeduplication.shouldShowNotification(messageId)) {
  Logger.w('Skipping duplicate notification: $messageId');
  return;
}
```

### 2. تتبع الإشعارات:
- يتم حفظ message ID مع الوقت الحالي
- فحص الإشعارات الجديدة مقابل السجل
- تنظيف السجلات القديمة تلقائياً

### 3. منع التكرار:
- إذا كان الإشعار معروض خلال آخر 5 دقائق، يتم تجاهله
- إذا كان جديد، يتم عرضه وإضافته للسجل

## المميزات

### ✅ منع التكرار الفعال
- لا مزيد من الإشعارات المكررة
- نافذة زمنية قابلة للتخصيص

### ✅ إدارة الذاكرة
- تنظيف تلقائي للسجلات القديمة
- حد أقصى لحجم السجل (100 إشعار)

### ✅ سجلات مفصلة
- تسجيل مفصل لجميع العمليات
- سهولة التتبع والتشخيص

### ✅ اختبارات شاملة
- اختبارات وحدة لآلية منع التكرار
- تغطية جميع الحالات المهمة

## الاستخدام

### تشغيل الاختبارات:
```bash
flutter test test/notification_deduplication_test.dart
```

### مراقبة السجلات:
```bash
flutter logs | grep "Notification\|Firebase"
```

## ملاحظات مهمة

1. **message ID**: يجب أن يكون فريد لكل إشعار
2. **النافذة الزمنية**: 5 دقائق افتراضياً، قابلة للتعديل
3. **حجم السجل**: 100 إشعار كحد أقصى
4. **التنظيف**: تلقائي عند انتهاء النافذة الزمنية

## التخصيص

يمكن تعديل الإعدادات في `notification_deduplication.dart`:

```dart
static const Duration _duplicateWindow = Duration(minutes: 5); // النافذة الزمنية
static const int _maxHistorySize = 100; // حجم السجل الأقصى
```
