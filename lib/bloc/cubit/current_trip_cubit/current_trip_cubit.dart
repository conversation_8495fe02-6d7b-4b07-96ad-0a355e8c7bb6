import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/bloc/cubit/current_trip_cubit/current_trip_states.dart';
import 'package:busaty_parents/data/repo/current_trip_repo.dart';

class CurrentTripCubit extends Cubit<CurrentTripStates> {
  CurrentTripCubit() : super(CurrentTripInitialStates());
  final _currentTripRepo = CurrentTripRepo();

  static CurrentTripCubit get(context) => BlocProvider.of(context);

  Future<void> getCurrentTrip() async {
    emit(CurrentTripLoadingStates());
    try {
      final response = await _currentTripRepo.repo();
      if (response.status == true&& response.data!.isNotEmpty) {
        // CacheHelper.putString(
        //     'createdBy', response.data!.first.bus!.trip!.first.createdBy!);
        emit(CurrentTripSuccessStates(currentTripModels: response));
        Logger.e(
            'getCurrentTrip-ppppppppppppppppppppppppppppppppppppppppppppppppp${response.data}');
      } else {
        emit(CurrentTripErrorStates(error: '========${response.message}'));
      }
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      Logger.e("++++++++++++++++++++++++++++++++++++catch error at cubits$e");
      emit(CurrentTripErrorStates(error: e.toString()));
    }
  }
}
