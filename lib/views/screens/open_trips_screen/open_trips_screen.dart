import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:busaty_parents/bloc/cubit/current_trip_cubit/current_trip_cubit.dart';
import 'package:busaty_parents/bloc/cubit/current_trip_cubit/current_trip_states.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/views/screens/show_son_on_map_screen/show_son_on_map_screen.dart';
import 'package:busaty_parents/widgets/custom_appbar.dart';

class OpenTripsScreen extends StatelessWidget {
  static const routeName = PathRouteName.openTripScreen;
  const OpenTripsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.openTrips.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            BlocBuilder<CurrentTripCubit, CurrentTripStates>(
              builder: (context, states) {
                if (states is CurrentTripLoadingStates) {
                  return const CircularProgressIndicator(
                    color: TColor.mainColor,
                  );
                } else if (states is CurrentTripSuccessStates) {
                  return Expanded(
                    child: ListView.separated(
                      itemCount: states.currentTripModels!.data!.length,
                      padding: EdgeInsets.symmetric(vertical: 20.w),
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: InkWell(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ShowSonOnTheMapScreen(
                                    // createdBy: states
                                    //     .currentTripModels!.data![index].bus!.trip!.first.createdBy! ,
                                    busId: states
                                        .currentTripModels!.data![index].bus_id!
                                        .toString(),
                                    logoPath: states.currentTripModels!
                                        .data![index].logo_path,
                                    studentId: states
                                        .currentTripModels!.data![index].id,
                                    sourceLat: double.parse(states
                                            .currentTripModels!
                                            .data![index]
                                            .latitude ??
                                        '0.0'),
                                    sourceLong: double.parse(states
                                            .currentTripModels!
                                            .data![index]
                                            .longitude ??
                                        '0.0'), //full_day
                                    tripType: states.currentTripModels!
                                        .data![index].bus!.trip![0].tripType,
                                    // tripType: states.currentTripModels!
                                    //     .data![index].bus!.trip![0].trip_type,
                                    destinationLat: double.parse(states
                                            .currentTripModels!
                                            .data![index]
                                            .schools!
                                            .latitude ??
                                        '0.0'),
                                    destinationLong: double.parse(states
                                            .currentTripModels!
                                            .data![index]
                                            .schools!
                                            .longitude ??
                                        '0.0'),
                                  ),
                                ),
                              );
                              // Logger().e('-----------------------------------${
                              //   states.currentTripModels!
                              //           .data![index].bus?.trip![index].tripType!
                              // }');
                            },
                            child: Container(
                              width: 354.w,
                              // height: 185.w,
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: TColor.borderContainer),
                                borderRadius: BorderRadius.circular(14.r),
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10.w, vertical: 10.w),
                                child: Column(
                                  children: [
                                    CircleAvatar(
                                      backgroundImage: NetworkImage(
                                        states.currentTripModels!.data![index]
                                            .logo_path!,
                                      ),
                                      radius: 30.r,
                                    ),
                                    12.verticalSpace,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                          text: "${AppStrings.name.tr()} :",
                                          color: TColor.black,
                                          fontW: FontWeight.w600,
                                          fontSize: 15,
                                        ),
                                        SizedBox(
                                          width: 200.w,
                                          child: CustomText(
                                            text: states.currentTripModels!
                                                .data![index].name,
                                            color: TColor.black,
                                            fontW: FontWeight.w500,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                    2.verticalSpace,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                          text: "${AppStrings.busName.tr()} :",
                                          color: TColor.black,
                                          fontW: FontWeight.w600,
                                          fontSize: 15,
                                        ),
                                        SizedBox(
                                          width: 200.w,
                                          child: CustomText(
                                            text: states.currentTripModels!
                                                .data![index].bus!.name,
                                            color: TColor.black,
                                            fontW: FontWeight.w500,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                    2.verticalSpace,
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return const Sbox(
                          h: 20,
                        );
                      },
                    ),
                  );
                } else if (states is CurrentTripErrorStates) {
                  return const SizedBox();
                } else {
                  return const SizedBox();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
