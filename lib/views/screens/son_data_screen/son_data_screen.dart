import 'dart:io';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:busaty_parents/bloc/cubit/son_by_id_cubit/son_by_id_cubit.dart';
import 'package:busaty_parents/bloc/cubit/son_by_id_cubit/son_by_id_states.dart';
import 'package:busaty_parents/bloc/cubit/upload_profile_image_cubit/upload_profile_image_cubit.dart';
import 'package:busaty_parents/bloc/cubit/upload_profile_image_cubit/upload_profile_image_states.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/views/custom_widgets/custom_snack_bar.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/widgets/custom_appbar.dart';
import 'package:busaty_parents/widgets/custom_student_c_w.dart';
import '../../../data/models/child_model.dart';
import '../show_son_on_map_screen/show_son_on_map_screen.dart';

class SonDataScreen extends StatefulWidget {
  static const String routeName = PathRouteName.sonData;
  final ChildModel son;

  const SonDataScreen({super.key, required this.son});

  @override
  State<SonDataScreen> createState() => _SonDataScreenState();
}

class _SonDataScreenState extends State<SonDataScreen> {
  File? image;

  Future<void> uploadImage() async {
    try {
      final result = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (result != null) {
        image = File(result.path);
        if (image != null) {
          context.read<UploadProfileImageCubit>().uploadImageProfile(
                image: image,
                studentId: widget.son.id.toString(),
              );
        }
        setState(() {});
      }
    } catch (e, stackTrace) {
      debugPrint("Catch error at uploadImage: $e");
      debugPrint(stackTrace.toString());
      print(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.sonData.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: _buildBackButton(context),
      ),
      body: BlocBuilder<SonByIdCubit, SonByIdStates>(
        builder: (context, state) {
          if (state is SonByIdLoadingStates) {
            return const Center(
              child: CircularProgressIndicator(
                color: TColor.mainColor,
              ),
            );
          } else if (state is SonByIdSuccessStates) {
            return _buildSuccessContent(state);
          } else {
            return const SizedBox();
          }
        },
      ),
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: SvgPicture.asset(
        context.locale.toString() == "ar"
            ? AppAssets.arrowBack
            : AppAssets.forwardArrow,
        colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
        width: 25.w,
        height: 25.w,
      ),
    );
  }

  Widget _buildSuccessContent(SonByIdSuccessStates state) {
    return SingleChildScrollView(
      child: SizedBox(
        height: 1.sh,
        width: 1.sw,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            20.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: _buildProfileImage(state),
            ),
            30.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: _buildStudentDetails(state),
            ),
            40.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: _buildShowOnMapButton(state),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage(SonByIdSuccessStates state) {
    return image != null
        ? BlocConsumer<UploadProfileImageCubit, UploadProfileImageStates>(
            listener: (context, state) {
              if (state is UploadProfileImageSuccessStates) {
                customSnackBar(
                  message: state.uploadImageModels!.message,
                  bgColor: TColor.greenSuccess,
                  txColor: TColor.white, context: context,
                );
                image = null;
                context.read<SonByIdCubit>().getSonById(
                      studentId: widget.son.id.toString(),
                    );
              } else if (state is UploadProfileImageErrorStates) {
                customSnackBar(
                  context: context,
                  message: state.error,
                  bgColor: TColor.redAccent,
                  txColor: TColor.white,
                );
                image = null;
              }
            },
            builder: (context, state) {
              return Stack(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.25),
                      shape: BoxShape.circle,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(2),
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.blue,
                          image: DecorationImage(
                            image: FileImage(image ?? File("")),
                            fit: BoxFit.fill,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    left: 0,
                    child: CircularProgressIndicator(
                      color: TColor.mainColor,
                    ),
                  ),
                ],
              );
            },
          )
        : Stack(
            clipBehavior: Clip.none,
            children: [
              InkWell(
                onTap: uploadImage,
                child: CircleAvatar(
                  radius: 50.r,
                  backgroundColor: TColor.borderContainer,
                  child: CircleAvatar(
                    radius: 49.r,
                    backgroundColor: TColor.white,
                    backgroundImage: NetworkImage(state
                            .studentIdModels!.data!.logoPath ??
                        "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                  ),
                ),
              ),
              Positioned(
                bottom: 0.w,
                right: -15.w,
                child: InkWell(
                  onTap: uploadImage,
                  child: const Icon(
                    Icons.add,
                    color: TColor.mainColor,
                    size: 40,
                  ),
                ),
              ),
            ],
          );
  }

  Widget _buildStudentDetails(SonByIdSuccessStates state) {
    return Column(
      children: [
        CustomSonCW(
          isLabel: true,
          label: AppStrings.name.tr(),
          name: state.studentIdModels!.data!.name,
        ),
        20.verticalSpace,
        CustomSonCW(
          isLabel: true,
          label: AppStrings.address.tr(),
          name: state.studentIdModels!.data!.address ?? "",
        ),
        20.verticalSpace,
        CustomSonCW(
          isLabel: true,
          label: AppStrings.gradeEdu.tr(),
          name: state.studentIdModels!.data!.grade?.name ?? "",
        ),
        20.verticalSpace,
        CustomSonCW(
          isLabel: true,
          label: AppStrings.school.tr(),
          name: state.studentIdModels!.data!.schools?.name ?? "",
        ),
        20.verticalSpace,
        CustomSonCW(
          isLabel: true,
          label: AppStrings.bus.tr(),
          name: state.studentIdModels!.data!.bus?.name ?? "",
        ),
      ],
    );
  }

  Widget _buildShowOnMapButton(SonByIdSuccessStates state) {
    return CustomButton(
      text: AppStrings.showSonOnMap.tr(),
      onTap: () {
        Navigator.push(context, MaterialPageRoute(builder: (ctx) {
          return ShowSonOnTheMapScreen(
          
            tripType: state.studentIdModels!.data!.tripType,
            busId: state.studentIdModels!.data!.busId.toString(),
            logoPath: state.studentIdModels!.data!.logoPath,
            sourceLat: state.studentIdModels!.data!.latitude != null
                ? double.parse(state.studentIdModels!.data!.latitude!)
                : 0.0,
            sourceLong: state.studentIdModels!.data!.longitude != null
                ? double.parse(state.studentIdModels!.data!.longitude!)
                : 0.0,
            destinationLat: state.studentIdModels!.data!.schools!.latitude !=
                    null
                ? double.parse(state.studentIdModels!.data!.schools!.latitude!)
                : 0.0,
            destinationLong: state.studentIdModels!.data!.schools!.longitude !=
                    null
                ? double.parse(state.studentIdModels!.data!.schools!.longitude!)
                : 0.0,
          );
        }));
      },
      width: 428,
      height: 53,
      radius: 15,
      borderColor: TColor.mainColor,
      bgColor: TColor.mainColor,
    );
  }
}
