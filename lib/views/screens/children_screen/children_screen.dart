import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:logger/logger.dart';
import 'package:busaty_parents/bloc/cubit/children_cubit/children_cubit.dart';
import 'package:busaty_parents/bloc/cubit/son_by_id_cubit/son_by_id_cubit.dart';
import 'package:busaty_parents/views/screens/add_son_screen/add_son_screen.dart';
import 'package:busaty_parents/views/screens/son_data_screen/son_data_screen.dart';
import 'package:busaty_parents/widgets/custom_appbar.dart';

import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../services/ad_mob_service.dart';
import '../../../services/payment_service.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../utils/helpers.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';
import '../show_son_on_map_screen/show_son_on_map_screen.dart';

class ChildrenScreen extends StatefulWidget {
  static const routeName = PathRouteName.childrenScreen;

  const ChildrenScreen({super.key});

  @override
  State<ChildrenScreen> createState() => _ChildrenScreenState();
}

class _ChildrenScreenState extends State<ChildrenScreen> {
  // Ad Related
  late AdMobService _adMobService;
  BannerAd? _banner;
  BannerAd? _banner1;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (PaymentService.instance.isProUser == false &&
        subscriptionStatus == false) {
      _adMobService = GetIt.instance.get<AdMobService>();
      _adMobService.initialization.then((value) {
        setState(() {
          _banner = BannerAd(
            adUnitId: _adMobService.bannerAdUnitId!,
            size: AdSize.fullBanner,
            request: const AdRequest(),
            listener: _adMobService.bannerListener,
          )..load();
          _banner1 = BannerAd(
            adUnitId: _adMobService.bannerAdUnitId!,
            size: AdSize.fullBanner,
            request: const AdRequest(),
            listener: _adMobService.bannerListener,
          )..load();
        });
      });
    } else {
      setState(() {
        _banner = null;
        _banner1 = null;
      });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    BlocProvider.of<ChildrenCubit>(context).getStudent();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.children.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: InkWell(
                onTap: () {
                  // BlocProvider.of<ChildrenCubit>(context).getStudent();

                  Navigator.push(context, MaterialPageRoute(builder: (ctx) {
                    return const AddSonScreen();
                  }));
                },
                child: Icon(
                  Icons.add_circle_outline,
                  size: 30.sp,
                  color: TColor.mainColor,
                ),
              ),
            ),
            10.verticalSpace,
            _banner1 == null
                ? const SizedBox()
                : SizedBox(
                    height: 60,
                    width: 1.sw,
                    child: AdWidget(ad: _banner1!),
                  ),
            BlocBuilder<ChildrenCubit, ChildrenState>(
              builder: (context, state) {
                if (state is ChildrenInitialState) {
                  BlocProvider.of<ChildrenCubit>(context).getStudent();
                  return const Center(
                      child: CircularProgressIndicator(
                    color: TColor.mainColor,
                  ));
                }
                if (state is ChildrenSuccessState) {
                  // BlocProvider.of<ChildrenCubit>(context).getStudent();

                  return Padding(
                    padding: EdgeInsets.all(8.0.w),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8.0),
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.0),
                      ),
                      child: Table(
                        columnWidths: const {
                          0: FlexColumnWidth(2),
                          1: FlexColumnWidth(2),
                          2: FlexColumnWidth(2),
                          3: FlexColumnWidth(1),
                        },
                        border: TableBorder.all(
                            color: TColor.tabColors,
                            borderRadius: BorderRadius.circular(15.0)),
                        children:
                            List.generate(1 + state.childrens!.length, (index) {
                          if (index == 0) {
                            print(state.childrens![index].id);
                            return BuildTableRowWidget(
                              cell: [
                                AppStrings.name.tr(),
                                AppStrings.school.tr(),
                                AppStrings.grade.tr(),
                                AppStrings.show.tr()
                              ],
                              header: true,
                            ).build(context);
                          } else {
                            final newChild = state.childrens![index - 1];
                            return BuildTableRowWidget(
                              onTapDown: (position) {
                                Helpers.customShowDialog(context,
                                    position: position.globalPosition,
                                    onTapShow: () {
                                  context.read<SonByIdCubit>().getSonById(
                                        studentId: state
                                            .childrens![index - 1].id
                                            .toString(),
                                      );
                                  Navigator.of(context)
                                    ..pop()
                                    ..push(MaterialPageRoute(builder: (ctx) {
                                      return SonDataScreen(
                                        son: state.childrens![index - 1],
                                      );
                                    }));
                                }, onTapMorningTrip: () {
                                  Logger().e(state.childrens![index - 1]);
                                  Navigator.of(context)
                                    ..pop()
                                    ..push(MaterialPageRoute(builder: (ctx) {
                                      return ShowSonOnTheMapScreen(
                                        
                                        busId: state.childrens![index - 1].busId
                                            .toString(),
                                        logoPath: state
                                            .childrens![index - 1].logoPath,
                                        studentId:
                                            state.childrens![index - 1].id!,
                                        sourceLat: double.parse(state
                                                .childrens![index - 1]
                                                .latitude ??
                                            '0.0'),
                                        // double.parse(),
                                        // state.childrens![index - 1].latitude,
                                        //  double.parse(state.childrens![index - 1].latitude.toString()),
                                        sourceLong: double.parse(state
                                                .childrens![index - 1]
                                                .longitude ??
                                            '0.0'),
                                        // double.parse(state
                                        //     .childrens![index - 1].longitude),
                                        destinationLat:
                                            // state
                                            //     .childrens![index - 1]
                                            //     .schools!
                                            //     .latitude!.to,
                                            double.parse(state
                                                .childrens![index - 1]
                                                .schools!
                                                .latitude!),
                                        destinationLong: double.parse(state
                                            .childrens![index - 1]
                                            .schools!
                                            .longitude!),
                                        tripType: 'start_day',
                                      );
                                    }));
                                }, onTapEveningTrip: () {
                                  Navigator.of(context)
                                    ..pop()
                                    ..push(MaterialPageRoute(builder: (ctx) {
                                      return ShowSonOnTheMapScreen(
                                      
                                        busId: state.childrens![index - 1].busId
                                            .toString(),
                                        logoPath: state
                                            .childrens![index - 1].logoPath,
                                        studentId:
                                            state.childrens![index - 1].id!,
                                        sourceLat: double.parse(state
                                                .childrens![index - 1]
                                                .latitude ??
                                            '0.0'),
                                        sourceLong: double.parse(state
                                                .childrens![index - 1]
                                                .longitude ??
                                            '0.0'),
                                        destinationLat: double.parse(state
                                            .childrens![index - 1]
                                            .schools!
                                            .latitude!),
                                        destinationLong: double.parse(state
                                            .childrens![index - 1]
                                            .schools!
                                            .longitude!),
                                        tripType: 'end_day',
                                      );
                                    }));
                                }, onTapDelete: () {
                                  Navigator.pop(context);
                                  BlocProvider.of<ChildrenCubit>(context)
                                      .deleteStudent(
                                          id: state.childrens![index - 1].id!);
                                });
                              },
                              cell: [
                                newChild.name ?? "",
                                newChild.schools?.name ?? "",
                                context.locale.toString() == "ar" &&
                                        newChild.grade?.name == 'Middle'
                                    ? 'الإعدادية'
                                    : context.locale.toString() == "ar" &&
                                            newChild.grade?.name == 'Primary'
                                        ? 'الإبتدائية'
                                        : context.locale.toString() == "ar" &&
                                                newChild.grade?.name ==
                                                    'secondary'
                                            ? 'الثانوية'
                                            : context.locale.toString() ==
                                                        "ar" &&
                                                    newChild.grade?.name == 'KG'
                                                ? 'الروضة'
                                                : newChild.grade?.name ?? "--",
                                Icons.more_horiz,
                              ],
                            ).build(context);
                          }
                        }),
                      ),
                    ),
                  );
                } else {
                  return Center(
                    child: SizedBox(
                      child: CustomText(
                        text: AppStrings.notFound.tr(),
                        fontSize: 18,
                        textAlign: TextAlign.center,
                        fontW: FontWeight.w600,
                        color: TColor.text,
                      ),
                    ),
                  );
                }
              },
            ),
            10.verticalSpace,
            _banner == null
                ? const SizedBox()
                : SizedBox(
                    height: 60,
                    // width: 1.sw,
                    child: AdWidget(ad: _banner!),
                  ),
          ],
        ),
      ),
    );
  }
}
