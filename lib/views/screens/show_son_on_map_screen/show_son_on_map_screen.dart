import 'dart:convert';
import 'package:busaty_parents/bloc/cubit/subscription_cubit/subscription_cubit.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/views/screens/pro_screen/pro_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:logger/logger.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;

import '../../../bloc/cubit/children_cubit/children_cubit.dart';
import '../../../config/config_base.dart';
import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';

class ShowSonOnTheMapScreen extends StatefulWidget {
  // final String createdBy;
  final String busId;
  final String? logoPath;
  final String? studentId;
  final String? tripType;
  final double? sourceLat;
  final double? sourceLong;
  final double destinationLat;
  final double destinationLong;

  const ShowSonOnTheMapScreen({
    super.key,
    required this.busId,
    this.logoPath,
    this.studentId,
    this.tripType,
    required this.destinationLat,
    required this.destinationLong,
    this.sourceLat,
    this.sourceLong,
    // required this.createdBy,
  });

  @override
  State<ShowSonOnTheMapScreen> createState() => _ShowSonOnTheMapScreenState();
}

class _ShowSonOnTheMapScreenState extends State<ShowSonOnTheMapScreen> {
  final io.Socket socket = io.io(ConfigBase.socketUrl, <String, dynamic>{
    'query': {
      'token': socketToken,
    },
    'transports': ['websocket'],
  });

  GeoPoint? previousLocation;
  GeoPoint? currentLocation;
  MapController? mapController;
  bool isLocationAvailable = false;
  bool isTripLoading = false;
  List<StaticPositionGeoPoint> staticPoints = [];

  @override
  void initState() {
    super.initState();
    Logger().e('=============================$subscriptionStatus');
    // set manually init position
    mapController = MapController.withPosition(
      initPosition: GeoPoint(
        latitude: currentLocation?.latitude ?? widget.sourceLat!,
        longitude: currentLocation?.longitude ?? widget.sourceLong!,
      ),
    );
    staticPoints.add(StaticPositionGeoPoint(
      'source',
      MarkerIcon(
        iconWidget: Container(
          height: 75.w,
          width: 75.w,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: AssetImage(
                "assets/images/marker_one.png",
              ),
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
      [
        GeoPoint(latitude: widget.sourceLat!, longitude: widget.sourceLong!),
      ],
    ));
    staticPoints.add(StaticPositionGeoPoint(
      'destination',
      MarkerIcon(
        iconWidget: Container(
          height: 75.w,
          width: 75.w,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: AssetImage("assets/images/marker_two.png"),
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
      [
        GeoPoint(
            latitude: widget.destinationLat, longitude: widget.destinationLong),
      ],
    ));
    socket.onConnect((_) {
      Logger().w('receiveLocationFromSocket: Connected to WebSocket ====== ');
    });
    getTripStatus();
  }

  @override
  void dispose() {
    socket.disconnect();
    mapController!.dispose();
    super.dispose();
  }

  void getTripStatus() async {
    final status = BlocProvider.of<ChildrenCubit>(context).tripStatus(
      studentId: widget.studentId.toString(),
      tripType: widget.tripType,
    );
    if (await status) {
      isTripLoading = true;
      Logger().w("this is loading trip $status and this is $isTripLoading");
      socket.connect();
      receiveLocationFromSocket();
    }
  }

  void receiveLocationFromSocket() {
    debugPrint("receiveLocationFromSocket method starts");

    final receiveEvent = widget.busId;

    socket.onConnect((_) {
      debugPrint('receiveLocationFromSocket: Connected to WebSocket');
      Logger().w('receiveLocationFromSocket: Connected to WebSocket ======  ');
    });

    socket.onDisconnect((reason) {
      debugPrint(
          'receiveLocationFromSocket: Disconnected from WebSocket  zaki - $reason');
      Logger().w(
          'receiveLocationFromSocket: Connected to WebSocket ====== $reason ');
    });

    socket.on(receiveEvent, (data) async {
      if (!mounted) return;

      try {
        setState(() {
          isTripLoading = false;
          isLocationAvailable = true;
        });

        debugPrint('>>>>>>>>>>> receiveBus Event');
        final locationJson = jsonDecode(data);
        debugPrint(locationJson.toString());

        double byLongitude = 0.0;
        double byLatitude = 0.0;

        byLongitude = double.parse(locationJson['longitude']);
        byLatitude = double.parse(locationJson['latitude']);
        currentLocation = GeoPoint(
          latitude: byLatitude,
          longitude: byLongitude,
        );

        if (mapController != null) {
          try {
            // Remove the previous marker before adding a new one
            if (previousLocation != null) {
              await mapController!.removeMarker(previousLocation!);
            }

            // Add the new marker
            await mapController!.addMarker(
              currentLocation!,
              markerIcon: MarkerIcon(
                iconWidget: CircleAvatar(
                  radius: 25.r,
                  backgroundColor: TColor.borderContainer,
                  child: CircleAvatar(
                    radius: 22.r,
                    backgroundColor: TColor.white,
                    backgroundImage: NetworkImage(widget.logoPath ?? ""),
                  ),
                ),
              ),
            );

            // Update map view
            await mapController!.goToLocation(currentLocation!);

            // Update previous location after successful marker update
            previousLocation = currentLocation;
          } catch (e) {
            Logger().e('Error updating map marker: $e');
          }
        } else {
          Logger().w('Map controller is null');
        }
      } catch (e) {
        Logger().e('Error processing location data: $e');
      }
    });
    debugPrint("receiveLocationFromSocket method ends");
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChildrenCubit, ChildrenState>(
      builder: (context, state) {
        return Scaffold(
            appBar: CustomAppBar(
              titleWidget: CustomText(
                text: AppStrings.sonLocation.tr(),
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.white,
              ),
              leftWidget: context.locale.toString() == "ar"
                  ? InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(AppAssets.arrowBack),
                    )
                  : InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(
                        AppAssets.forwardArrow,
                        colorFilter: const ColorFilter.mode(
                            TColor.white, BlendMode.srcIn),
                        width: 25.w,
                        height: 25.w,
                      ),
                    ),
            ),
            body: BlocConsumer<SubscriptionCubit, SubscriptionState>(
              builder: (context, state) {
                if (subscriptionStatus == true || isPro == true) {
                  if (isLocationAvailable) {
                    return OSMFlutter(
                      controller: mapController!,
                      mapIsLoading: const SizedBox(
                        height: 10,
                        width: 10,
                        child: Center(
                          child: CircularProgressIndicator(
                            color: TColor.mainColor,
                          ),
                        ),
                      ),
                      osmOption: OSMOption(
                        enableRotationByGesture: true,
                        showZoomController: true,
                        staticPoints: staticPoints,
                        zoomOption: const ZoomOption(
                          initZoom: 15,
                          minZoomLevel: 3,
                          maxZoomLevel: 19,
                          stepZoom: 1.0,
                        ),
                      ),
                      onMapIsReady: (ready) {},
                    );
                  } else {
                    if (isTripLoading != false) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CircularProgressIndicator(
                              color: TColor.mainColor,
                            ),
                            20.verticalSpace,
                            Card(
                              child: Padding(
                                padding: const EdgeInsets.all(30.0),
                                child: CustomText(
                                  text: AppStrings.tripLoading.tr(),
                                  fontSize: 20,
                                  fontW: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    } else {
                      return Center(
                        child: Card(
                          child: Padding(
                            padding: const EdgeInsets.all(30.0),
                            child: CustomText(
                              text: AppStrings.noTrips.tr(),
                              fontSize: 20,
                              fontW: FontWeight.w600,
                            ),
                          ),
                        ),
                      );
                    }
                  }
                } else {
                  return SafeArea(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            TColor.white,
                            TColor.mainColor.withOpacity(0.1),
                          ],
                        ),
                      ),
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 24.w),
                          child: Card(
                            elevation: 8,
                            shadowColor: TColor.mainColor.withOpacity(0.2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Padding(
                              padding: EdgeInsets.all(24.w),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(16.w),
                                    decoration: BoxDecoration(
                                      color: TColor.mainColor.withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.location_on_rounded,
                                      size: 48.w,
                                      color: TColor.mainColor,
                                    ),
                                  ),
                                  SizedBox(height: 24.h),
                                  CustomText(
                                    text: AppStrings.subscribeToTrack.tr(),
                                    color: TColor.black,
                                    fontW: FontWeight.w700,
                                    fontSize: 22,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 12.h),
                                  CustomText(
                                    text: AppStrings.trackingDescription.tr(),
                                    color: TColor.dialogName,
                                    fontSize: 14,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 24.h),
                                  AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    child: ElevatedButton(
                                      onPressed: () {
                                        Navigator.pushNamed(
                                            context, PROScreen.routeName);
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: TColor.mainColor,
                                        foregroundColor: TColor.white,
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 32.w,
                                          vertical: 12.h,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        elevation: 2,
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.star_rounded, size: 20.w),
                                          SizedBox(width: 8.w),
                                          CustomText(
                                            text: AppStrings.subscribeNow.tr(),
                                            color: TColor.white,
                                            fontW: FontWeight.w600,
                                            fontSize: 16,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }
              },
              listener: (context, state) {},
            ));
      },
    );
  }
}
