import 'package:flutter_test/flutter_test.dart';
import 'package:busaty_parents/notification_service/utils/notification_deduplication.dart';

void main() {
  group('NotificationDeduplication', () {
    setUp(() {
      // Clear history before each test
      NotificationDeduplication.clearHistory();
    });

    test('should allow first notification', () {
      const messageId = 'test_message_1';
      
      final shouldShow = NotificationDeduplication.shouldShowNotification(messageId);
      
      expect(shouldShow, isTrue);
      expect(NotificationDeduplication.historySize, equals(1));
    });

    test('should block duplicate notification within window', () {
      const messageId = 'test_message_1';
      
      // First notification should be allowed
      final firstShow = NotificationDeduplication.shouldShowNotification(messageId);
      expect(firstShow, isTrue);
      
      // Second notification with same ID should be blocked
      final secondShow = NotificationDeduplication.shouldShowNotification(messageId);
      expect(secondShow, isFalse);
      
      expect(NotificationDeduplication.historySize, equals(1));
    });

    test('should allow different message IDs', () {
      const messageId1 = 'test_message_1';
      const messageId2 = 'test_message_2';
      
      final firstShow = NotificationDeduplication.shouldShowNotification(messageId1);
      final secondShow = NotificationDeduplication.shouldShowNotification(messageId2);
      
      expect(firstShow, isTrue);
      expect(secondShow, isTrue);
      expect(NotificationDeduplication.historySize, equals(2));
    });

    test('should maintain history size limit', () {
      // Add more than the maximum history size
      for (int i = 0; i < 150; i++) {
        NotificationDeduplication.shouldShowNotification('message_$i');
      }
      
      // History should be limited to max size
      expect(NotificationDeduplication.historySize, lessThanOrEqualTo(100));
    });

    test('should clear history when requested', () {
      // Add some notifications
      NotificationDeduplication.shouldShowNotification('message_1');
      NotificationDeduplication.shouldShowNotification('message_2');
      
      expect(NotificationDeduplication.historySize, equals(2));
      
      // Clear history
      NotificationDeduplication.clearHistory();
      
      expect(NotificationDeduplication.historySize, equals(0));
    });
  });
}
